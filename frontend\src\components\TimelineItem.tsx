
import { useState } from 'react';
import { useSpring, animated } from 'react-spring';
import { useDrag } from '@use-gesture/react';
import { ItineraryItem } from '@/pages/Index';
import { 
  Plane, 
  Hotel, 
  MapPin, 
  Utensils, 
  Car,
  Clock,
  ExternalLink,
  Edit3,
  Star,
  ChevronDown,
  ChevronUp,
  Trash2
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardHeader, CardContent } from '@/components/ui/card';

interface TimelineItemProps {
  item: ItineraryItem;
  index: number;
  onUpdate?: (itemId: string) => void;
  onDelete?: (itemId: string) => void;
}

const getTypeIcon = (type: ItineraryItem['type']) => {
  const iconClass = "w-5 h-5";
  switch (type) {
    case 'flight': return <Plane className={iconClass} />;
    case 'hotel': return <Hotel className={iconClass} />;
    case 'activity': return <Star className={iconClass} />;
    case 'restaurant': return <Utensils className={iconClass} />;
    case 'transport': return <Car className={iconClass} />;
    default: return <MapPin className={iconClass} />;
  }
};

const getTypeColor = (type: ItineraryItem['type']) => {
  switch (type) {
    case 'flight': return 'bg-primary text-primary-foreground border-primary/20';
    case 'hotel': return 'bg-blue-600 text-white border-blue-600/20';
    case 'activity': return 'bg-green-600 text-white border-green-600/20';
    case 'restaurant': return 'bg-orange-600 text-white border-orange-600/20';
    case 'transport': return 'bg-purple-600 text-white border-purple-600/20';
    default: return 'bg-primary text-primary-foreground border-primary/20';
  }
};

export const TimelineItem = ({ item, index, onUpdate, onDelete }: TimelineItemProps) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [showDelete, setShowDelete] = useState(false);
  const [isDeleted, setIsDeleted] = useState(false);
  const iconColor = getTypeColor(item.type);
  
  const [{ x }, api] = useSpring(() => ({ x: 0 }));
  
  const bind = useDrag(
    ({ down, movement: [mx], direction: [xDir], cancel }) => {
      if (window.innerWidth >= 768 || !onDelete) return; // Only on mobile and when delete is enabled
      
      const trigger = Math.abs(mx) > 50;
      const dir = xDir < 0 ? -1 : 1;
      const deleteButtonWidth = 80; // Fixed delete button width
      
      if (down && dir === -1 && mx < 0) {
        // While dragging left, show delete button and limit movement
        setShowDelete(true);
        api.start({ x: Math.max(mx, -deleteButtonWidth) });
      } else if (trigger && dir === -1) {
        // Trigger threshold reached, snap to delete position
        setShowDelete(true);
        api.start({ x: -deleteButtonWidth });
        cancel();
      } else if (!down) {
        // Released - decide whether to snap back or stay in delete position
        if (mx < -30) {
          // Keep in swiped state if dragged far enough
          setShowDelete(true);
          api.start({ x: -deleteButtonWidth });
        } else {
          // Snap back to original position
          setShowDelete(false);
          api.start({ x: 0 });
        }
      }
    },
    { axis: 'x', threshold: 5 }
  );

  const handleDelete = () => {
    if (onDelete) {
      setIsDeleted(true);
      api.start({
        x: -window.innerWidth,
        config: { tension: 200, friction: 20 },
        onRest: () => onDelete(item.id)
      });
    }
  };

  const resetSwipe = () => {
    setShowDelete(false);
    api.start({ x: 0 });
  };
  
  return (
    <div className={`relative mb-4 transition-all duration-500 ease-out ${isDeleted ? 'opacity-0 scale-y-0 -mb-4 h-0 overflow-hidden' : 'opacity-100 scale-y-100'}`}>
      <div className="relative flex items-start">
        {/* Timeline Dot */}
        <div className="relative z-10 flex-shrink-0 mt-2">
          <div className={`
            w-10 h-10 rounded-full ${iconColor}
            flex items-center justify-center shadow-sm
            border-2 border-background
          `}>
            {getTypeIcon(item.type)}
          </div>
        </div>

        {/* Content Container with proper width constraints */}
        <div className="ml-4 flex-1 relative overflow-hidden">
          <animated.div 
            {...bind()}
            style={{ x, touchAction: 'pan-y' }}
            className="relative w-full"
          >
            <Card 
              className="cursor-pointer hover:shadow-md transition-all duration-200 border-border bg-card shadow-sm hover:shadow-lg w-full"
              onClick={() => {
                if (showDelete) {
                  resetSwipe();
                } else {
                  setIsExpanded(!isExpanded);
                }
              }}
            >
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between min-w-0 gap-2">
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-1 mb-1">
                      <Clock className="w-3 h-3 text-muted-foreground flex-shrink-0" />
                      <span className="text-xs text-muted-foreground font-medium">{item.time.substring(0, 5)}</span>
                    </div>
                    <h3 className="font-semibold text-foreground text-sm md:text-base leading-tight">
                      {item.title}
                    </h3>
                  </div>
                  <div className="flex items-center gap-1 flex-shrink-0">
                    {onUpdate && (
                      <Button 
                        variant="ghost" 
                        size="sm" 
                        className="h-7 w-7 p-0"
                        onClick={(e) => {
                          e.stopPropagation();
                          onUpdate(item.id);
                        }}
                      >
                        <Edit3 className="w-3 h-3" />
                      </Button>
                    )}
                    {isExpanded ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />}
                  </div>
                </div>
              </CardHeader>
          
          {isExpanded && (
            <CardContent className="pt-0 animate-fade-in">
              <div className="space-y-3">
                {item.location && (
                  <div className="text-sm text-muted-foreground flex items-center gap-1">
                    <MapPin className="w-3 h-3" />
                    {item.location.includes('maps.google.com') ? (
                      <Button
                        variant="link"
                        className="p-0 h-auto text-sm text-primary hover:text-primary/80 underline"
                        onClick={(e) => {
                          e.stopPropagation();
                          window.open(item.location, '_blank');
                        }}
                      >
                        View on Map
                      </Button>
                    ) : (
                      <span>{item.location}</span>
                    )}
                  </div>
                )}
                
                <p className="text-sm text-foreground leading-relaxed">
                  {item.description}
                </p>

                {item.links && item.links.length > 0 && (
                  <div className="flex flex-wrap gap-2 pt-2">
                    {item.links.map((link, linkIndex) => (
                      <Button
                        key={linkIndex}
                        variant="outline"
                        size="sm"
                        className="text-xs h-7"
                        onClick={(e) => {
                          e.stopPropagation();
                          window.open(link.url, '_blank');
                        }}
                      >
                        <ExternalLink className="w-3 h-3 mr-1" />
                        {link.name}
                      </Button>
                    ))}
                  </div>
                )}
                
              </div>
            </CardContent>
          )}
            </Card>
          </animated.div>

          {/* Delete Button - Shows when swiped */}
          {showDelete && (
            <div className="absolute right-0 top-0 h-full flex items-center z-10">
              <Button
                variant="destructive"
                size="sm"
                className="h-full rounded-l-none px-4 shadow-lg min-w-[80px]"
                onClick={(e) => {
                  e.stopPropagation();
                  handleDelete();
                }}
              >
                <Trash2 className="w-4 h-4" />
              </Button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
