
import { DayData, ItineraryItem } from '@/pages/Index';
import { TimelineItem } from './TimelineItem';

interface TimelineProps {
  day: DayData;
  onItemUpdate?: (itemId: string) => void;
  onItemDelete?: (itemId: string) => void;
}

export const Timeline = ({ day, onItemUpdate, onItemDelete }: TimelineProps) => {
  return (
    <div className="relative">
      {/* Timeline Header */}
      <div className="mb-8">
        <div className="bg-card backdrop-blur-sm rounded-xl p-6 border border-border">
          <h2 className="text-2xl font-bold text-foreground mb-2">{day.dayName}</h2>
          <p className="text-muted-foreground">
            {new Date(day.date).toLocaleDateString('en-US', { 
              weekday: 'long', 
              year: 'numeric', 
              month: 'long', 
              day: 'numeric' 
            })}
          </p>
        </div>
      </div>

      {/* Timeline */}
      <div className="relative">
        {/* Timeline Line */}
        <div className="absolute left-5 top-0 bottom-0 w-0.5 bg-primary/50 dark:bg-primary/70"></div>
        
        {/* Timeline Items */}
        <div className="space-y-4">
          {day.items.map((item, index) => (
            <TimelineItem 
              key={item.id} 
              item={item} 
              index={index}
              onUpdate={onItemUpdate}
              onDelete={onItemDelete}
            />
          ))}
        </div>

        {/* Timeline End */}
        <div className="flex items-center justify-center mt-8">
          <div className="flex items-center px-4 py-2 rounded-lg bg-card/80 backdrop-blur-sm border border-border">
            <div className="w-4 h-4 bg-primary rounded-full shadow-sm border-2 border-background"></div>
            <div className="ml-3 text-muted-foreground italic font-medium text-sm">End of day</div>
          </div>
        </div>
      </div>
    </div>
  );
};
