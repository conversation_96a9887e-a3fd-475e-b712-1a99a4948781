import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { useItinerary } from '@/hooks/useItinerary';
import { TravelHeader } from '@/components/TravelHeader';
import { CreateItineraryModal } from '@/components/CreateItineraryModal';
import { ProfileDropdown } from '@/components/ProfileDropdown';
import { MapPin, Plus, Calendar, Users, Share2, Edit3, Trash2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';

const ItineraryList = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { 
    itineraries: allItineraries, 
    createItinerary: createItineraryService, 
    deleteItinerary: deleteItineraryService,
    generateShareableLink 
  } = useItinerary();
  const { toast } = useToast();
  
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);

  // Redirect to home page if user is not authenticated
  useEffect(() => {
    if (!user) {
      navigate('/');
    }
  }, [user, navigate]);

  const generateThumbnailFromTitle = async (title: string): Promise<string> => {
    const keywords = title.toLowerCase();
    
    if (keywords.includes('rajasthan') || keywords.includes('jaipur') || keywords.includes('udaipur')) {
      return '/src/assets/rajasthan-thumbnail.jpg';
    } else if (keywords.includes('paris') || keywords.includes('france')) {
      return 'https://images.unsplash.com/photo-1431274172761-fca41d930114?w=400&h=300&fit=crop&auto=format';
    } else if (keywords.includes('tokyo') || keywords.includes('japan')) {
      return 'https://images.unsplash.com/photo-1540959733332-eab4deabeeaf?w=400&h=300&fit=crop&auto=format';
    } else if (keywords.includes('new york') || keywords.includes('nyc')) {
      return 'https://images.unsplash.com/photo-1496442226666-8d4d0e62e6e9?w=400&h=300&fit=crop&auto=format';
    } else if (keywords.includes('london') || keywords.includes('uk') || keywords.includes('england')) {
      return 'https://images.unsplash.com/photo-1513635269975-59663e0ac1ad?w=400&h=300&fit=crop&auto=format';
    } else if (keywords.includes('mountain') || keywords.includes('himalaya') || keywords.includes('alps')) {
      return 'https://images.unsplash.com/photo-1464822759844-d150baec328b?w=400&h=300&fit=crop&auto=format';
    } else if (keywords.includes('beach') || keywords.includes('maldives') || keywords.includes('bali')) {
      return 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=300&fit=crop&auto=format';
    } else if (keywords.includes('europe')) {
      return 'https://images.unsplash.com/photo-1471623432079-b009d30b6729?w=400&h=300&fit=crop&auto=format';
    } else if (keywords.includes('asia')) {
      return 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=300&fit=crop&auto=format';
    } else if (keywords.includes('america') || keywords.includes('usa')) {
      return 'https://images.unsplash.com/photo-1501594907352-04cda38ebc29?w=400&h=300&fit=crop&auto=format';
    } else {
      return 'https://images.unsplash.com/photo-1488590528505-98d2b5aba04b?w=400&h=300&fit=crop&auto=format';
    }
  };

  const createItinerary = async (title: string, startDate: string, endDate: string, thumbnail?: string) => {
    try {
      const defaultThumbnail = thumbnail || await generateThumbnailFromTitle(title);
      
      const newItinerary = await createItineraryService(title, startDate, endDate, defaultThumbnail);
      navigate(`/itinerary/${newItinerary.id}`);
      
      toast({
        title: "Success!",
        description: "Your itinerary has been created.",
      });
    } catch (error) {
      console.error('Error creating itinerary:', error);
      toast({
        title: "Error",
        description: "Failed to create itinerary. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleShareItinerary = async (itineraryId: string, title: string) => {
    try {
      const shareableUrl = await generateShareableLink(itineraryId);
      const fullUrl = `${window.location.origin}/?share=${shareableUrl}&readonly=true`;
      
      await navigator.clipboard.writeText(fullUrl);
      toast({
        title: "Link copied!",
        description: `Share link for "${title}" has been copied to clipboard.`,
      });
    } catch (error) {
      console.error('Error sharing itinerary:', error);
      toast({
        title: "Error",
        description: "Failed to generate share link.",
        variant: "destructive",
      });
    }
  };

  const handleDeleteItinerary = async (itineraryId: string, title: string) => {
    if (window.confirm(`Are you sure you want to delete "${title}"?`)) {
      try {
        await deleteItineraryService(itineraryId);
        toast({
          title: "Deleted",
          description: `"${title}" has been deleted.`,
        });
      } catch (error) {
        console.error('Error deleting itinerary:', error);
        toast({
          title: "Error",
          description: "Failed to delete itinerary.",
          variant: "destructive",
        });
      }
    }
  };

  if (!user) {
    return (
      <div className="min-h-screen bg-background font-jakarta">
        <TravelHeader />
        <div className="container mx-auto px-4 py-16 text-center">
          <h1 className="text-4xl font-bold text-foreground mb-4">
            Welcome to TravelPlan
          </h1>
          <p className="text-xl text-muted-foreground mb-8">
            Please sign in to view your itineraries
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background font-jakarta">
      <TravelHeader showThemeToggle={false} />
      
      <div className="absolute top-4 right-4 z-50">
        <ProfileDropdown />
      </div>
      
      <div className="container mx-auto px-4 py-16">
        {allItineraries.length === 0 ? (
          <div className="max-w-2xl mx-auto text-center">
            <div className="mb-8">
              <h1 className="text-5xl font-bold text-foreground mb-4">
                Welcome to TravelPlan
              </h1>
              <p className="text-xl text-muted-foreground">
                Your smart travel companion for creating beautiful itineraries
              </p>
            </div>
            
            <div className="bg-card border border-border rounded-3xl p-8 shadow-lg">
              <div className="w-24 h-24 bg-primary rounded-full flex items-center justify-center mx-auto mb-6">
                <MapPin className="w-12 h-12 text-primary-foreground" />
              </div>
              
              <Button 
                onClick={() => setIsCreateModalOpen(true)}
                className="bg-primary hover:bg-primary/90 text-primary-foreground px-8 py-3 text-lg font-semibold rounded-2xl"
              >
                Create Your First Itinerary
              </Button>
            </div>
          </div>
        ) : (
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-12">
              <h1 className="text-4xl font-bold text-foreground mb-4">
                Your Travel Itineraries
              </h1>
              <p className="text-xl text-muted-foreground mb-8">
                Manage and view all your planned adventures
              </p>
              <Button 
                onClick={() => setIsCreateModalOpen(true)}
                className="bg-primary hover:bg-primary/90 text-primary-foreground px-6 py-3 rounded-2xl"
              >
                <Plus className="w-5 h-5 mr-2" />
                Create New Itinerary
              </Button>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {allItineraries.map((itinerary) => (
                <div 
                  key={itinerary.id} 
                  className="bg-card border border-border rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 group cursor-pointer"
                >
                  <div 
                    className="relative h-48 bg-cover bg-center"
                    style={{ 
                      backgroundImage: `url(${itinerary.thumbnail || 'https://images.unsplash.com/photo-1488590528505-98d2b5aba04b?w=400&h=300&fit=crop&auto=format'})` 
                    }}
                    onClick={() => navigate(`/itinerary/${itinerary.id}`)}
                  >
                    <div className="absolute inset-0 bg-black/30 group-hover:bg-black/20 transition-colors duration-300" />
                    <div className="absolute top-3 right-3 flex space-x-2">
                      <Button
                        size="sm"
                        variant="secondary"
                        className="bg-white/80 hover:bg-white text-gray-800 p-2"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleShareItinerary(itinerary.id, itinerary.title);
                        }}
                      >
                        <Share2 className="w-4 h-4" />
                      </Button>
                      <Button
                        size="sm"
                        variant="destructive"
                        className="bg-red-500/80 hover:bg-red-500 text-white p-2"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDeleteItinerary(itinerary.id, itinerary.title);
                        }}
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                  
                  <div 
                    className="p-6"
                    onClick={() => navigate(`/itinerary/${itinerary.id}`)}
                  >
                    <h3 className="text-xl font-bold text-foreground mb-2 group-hover:text-primary transition-colors">
                      {itinerary.title}
                    </h3>
                    
                    <div className="flex items-center text-muted-foreground text-sm mb-3">
                      <Calendar className="w-4 h-4 mr-2" />
                      <span>
                        {new Date(itinerary.startDate).toLocaleDateString()} - {new Date(itinerary.endDate).toLocaleDateString()}
                      </span>
                    </div>
                    
                    <div className="flex items-center text-muted-foreground text-sm">
                      <Users className="w-4 h-4 mr-2" />
                      <span>{itinerary.days.length} day{itinerary.days.length !== 1 ? 's' : ''}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      <CreateItineraryModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onCreateItinerary={createItinerary}
      />
    </div>
  );
};

export default ItineraryList;
