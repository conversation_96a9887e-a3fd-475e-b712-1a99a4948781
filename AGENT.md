# AGENT.md - Itinerary Stellar Flow

## Commands
- **Dev**: `npm run dev` (port 8080)
- **Build**: `npm run build` (production) or `npm run build:dev` (development)
- **Lint**: `npm run lint` (ESLint with TypeScript)
- **Preview**: `npm run preview`
- **Backend Dev**: `cd backend && npm run dev` (backend services)

## Architecture
- **Tech Stack**: React 18 + TypeScript + Vite + shadcn/ui + Tailwind CSS + Supabase
- **Authentication**: Supabase Auth with email/password
- **Database**: PostgreSQL via Supabase with Row Level Security (RLS)
- **State Management**: TanStack Query for server state + custom hooks
- **Routing**: React Router DOM
- **UI Components**: shadcn/ui (full component library in `src/components/ui/`)
- **Styling**: Tailwind CSS with CSS variables for theming

## Project Structure
- `src/components/` - Custom React components
- `src/components/ui/` - shadcn/ui components 
- `src/pages/` - Route components (Index, NotFound)
- `src/lib/` - Utilities (cn function, Supabase client)
- `src/hooks/` - Custom React hooks (useAuth, useItinerary)
- `src/contexts/` - React contexts (AuthContext)
- `backend/` - Backend services and database layer
- `backend/src/services/` - Supabase service layer
- `backend/src/types/` - TypeScript types for database
- `@/` - Alias for `src/` directory

## Environment Variables
- `VITE_SUPABASE_URL` - Supabase project URL
- `VITE_SUPABASE_ANON_KEY` - Supabase anonymous key
- Copy `.env.example` to `.env.local` and configure

## Database Schema
- **profiles**: User profiles (auto-created on signup)
- **itineraries**: Travel itineraries with user association
- **days**: Individual days within itineraries
- **itinerary_items**: Activities/items within days
- **RLS Policies**: Users can only access their own data

## Code Style
- **TypeScript**: Relaxed config (noImplicitAny: false, strictNullChecks: false)
- **Imports**: Use `@/` alias for src imports
- **Components**: PascalCase files, default exports
- **Styling**: Tailwind classes with cn() utility for conditional classes
- **Forms**: React Hook Form with Zod validation
- **Authentication**: Required for all operations except read-only mode
- **No unused vars**: ESLint rule disabled for flexibility
