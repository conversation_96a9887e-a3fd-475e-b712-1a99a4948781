
import { DayData } from '@/pages/Index';
import { Calendar } from 'lucide-react';

interface DayTabsProps {
  days: DayData[];
  selectedDay: string;
  onDaySelect: (dayId: string) => void;
}

const DayTab = ({ day, isSelected, onSelect }: {
  day: DayData;
  isSelected: boolean;
  onSelect: () => void;
}) => {
  return (
    <div className="relative flex-shrink-0">
      <div
        className={`
          relative px-2 md:px-4 py-2 rounded-lg transition-all duration-200 cursor-pointer
          ${isSelected 
            ? 'bg-primary text-primary-foreground shadow-sm' 
            : 'hover:bg-muted text-muted-foreground'
          }
        `}
        onClick={onSelect}
      >
        <div className="flex items-center gap-1 md:gap-2">
          <Calendar className="w-3 h-3 md:w-4 md:h-4 flex-shrink-0" />
          <span className="font-medium text-xs md:text-sm whitespace-nowrap min-w-0">
            {day.dayName}
          </span>
          <span className="text-xs opacity-70 hidden sm:inline whitespace-nowrap">
            {new Date(day.date).toLocaleDateString('en-US', { 
              month: 'short', 
              day: 'numeric' 
            })}
          </span>
        </div>
      </div>
    </div>
  );
};

export const DayTabs = ({ days, selectedDay, onDaySelect }: DayTabsProps) => {
  return (
    <div className="mb-8">
      <div className="overflow-x-auto pb-2 md:pb-0 md:overflow-x-visible">
        <div className="flex gap-1 md:gap-3 p-1 bg-card/80 backdrop-blur-sm rounded-xl border border-border w-fit md:w-full">
          {days.map((day) => (
            <DayTab
              key={day.id}
              day={day}
              isSelected={selectedDay === day.id}
              onSelect={() => onDaySelect(day.id)}
            />
          ))}
        </div>
      </div>
    </div>
  );
};
