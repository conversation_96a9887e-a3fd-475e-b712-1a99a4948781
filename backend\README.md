# Itinerary Stellar Flow - Backend

Backend services and database layer for the Itinerary application.

## Deployment on Vercel

### Environment Variables
Set these in Vercel dashboard:
- `SUPABASE_URL` - Your Supabase project URL  
- `SUPABASE_ANON_KEY` - Your Supabase anonymous key
- `SUPABASE_SERVICE_ROLE_KEY` - Your Supabase service role key (if needed)

### Build Settings
Vercel will automatically detect this as a Node.js project.

## Local Development
```bash
npm install
npm run dev
```

## Available Scripts
- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm start` - Start production server
