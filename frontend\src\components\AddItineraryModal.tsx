
import { useState, useEffect } from 'react';
import { ItineraryItem } from '@/pages/Index';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Plus, X } from 'lucide-react';

interface AddItineraryModalProps {
  isOpen: boolean;
  onClose: () => void;
  onAdd: (item: Omit<ItineraryItem, 'id'>) => void;
  selectedDay: string;
  editingItem?: ItineraryItem | null;
}

export const AddItineraryModal = ({ isOpen, onClose, onAdd, selectedDay, editingItem }: AddItineraryModalProps) => {
  const [formData, setFormData] = useState({
    time: editingItem?.time || '',
    title: editingItem?.title || '',
    location: editingItem?.location || '',
    description: editingItem?.description || '',
    type: (editingItem?.type || 'activity') as ItineraryItem['type'],
    links: editingItem?.links || [{ name: '', url: '' }]
  });

  // Update form data when editing item changes
  useEffect(() => {
    if (editingItem) {
      setFormData({
        time: editingItem.time,
        title: editingItem.title,
        location: editingItem.location,
        description: editingItem.description,
        type: editingItem.type,
        links: editingItem.links || [{ name: '', url: '' }]
      });
    } else {
      setFormData({
        time: '',
        title: '',
        location: '',
        description: '',
        type: 'activity',
        links: [{ name: '', url: '' }]
      });
    }
  }, [editingItem]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    const validLinks = formData.links.filter(link => link.name && link.url);
    
    onAdd({
      ...formData,
      links: validLinks.length > 0 ? validLinks : undefined
    });
    
    // Reset form
    setFormData({
      time: '',
      title: '',
      location: '',
      description: '',
      type: 'activity',
      links: [{ name: '', url: '' }]
    });
    
    onClose();
  };

  const addLinkField = () => {
    setFormData(prev => ({
      ...prev,
      links: [...prev.links, { name: '', url: '' }]
    }));
  };

  const removeLinkField = (index: number) => {
    setFormData(prev => ({
      ...prev,
      links: prev.links.filter((_, i) => i !== index)
    }));
  };

  const updateLink = (index: number, field: 'name' | 'url', value: string) => {
    setFormData(prev => ({
      ...prev,
      links: prev.links.map((link, i) => 
        i === index ? { ...link, [field]: value } : link
      )
    }));
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold">
            {editingItem ? `Edit Item for ${selectedDay}` : `Add New Item to ${selectedDay}`}
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="time" className="text-sm font-semibold text-foreground">Time</Label>
            <div className="relative cursor-pointer">
                <Input
                  id="time"
                  type="time"
                  value={formData.time}
                  onChange={(e) => setFormData(prev => ({ ...prev, time: e.target.value }))}
                  className="bg-background border-border rounded-xl text-lg font-mono cursor-pointer w-full"
                  required
                />
                <div className="absolute inset-0 cursor-pointer" onClick={() => document.getElementById('time')?.focus()}></div>
              </div>
            </div>
            
            <div>
              <Label htmlFor="type">Type</Label>
              <Select value={formData.type} onValueChange={(value) => setFormData(prev => ({ ...prev, type: value as ItineraryItem['type'] }))}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="flight">Flight</SelectItem>
                  <SelectItem value="hotel">Hotel</SelectItem>
                  <SelectItem value="activity">Activity</SelectItem>
                  <SelectItem value="restaurant">Restaurant</SelectItem>
                  <SelectItem value="transport">Transport</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div>
            <Label htmlFor="title">Title</Label>
            <Input
              id="title"
              value={formData.title}
              onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
              placeholder="e.g., Visit Sky Tower"
              required
            />
          </div>


          <div>
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              placeholder="Describe what you'll be doing..."
              rows={3}
            />
          </div>

          <div>
            <Label htmlFor="location">Google Maps Link (Optional)</Label>
            <Input
              id="location"
              value={formData.location}
              onChange={(e) => setFormData(prev => ({ ...prev, location: e.target.value }))}
              placeholder="e.g., https://maps.google.com/..."
            />
          </div>

          <div>
            <div className="flex items-center justify-between mb-3">
              <Label>Custom Links (Optional)</Label>
              <Button type="button" variant="outline" size="sm" onClick={addLinkField}>
                <Plus className="w-4 h-4 mr-1" />
                Add Link
              </Button>
            </div>
            
            <div className="space-y-3">
              {formData.links.map((link, index) => (
                <div key={index} className="flex gap-2">
                  <Input
                    placeholder="Link name"
                    value={link.name}
                    onChange={(e) => updateLink(index, 'name', e.target.value)}
                  />
                  <Input
                    placeholder="URL"
                    value={link.url}
                    onChange={(e) => updateLink(index, 'url', e.target.value)}
                  />
                  {formData.links.length > 1 && (
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => removeLinkField(index)}
                    >
                      <X className="w-4 h-4" />
                    </Button>
                  )}
                </div>
              ))}
            </div>
          </div>

          <div className="flex gap-3 pt-4">
            <Button type="submit" className="flex-1 bg-primary hover:bg-primary/90 text-primary-foreground rounded-xl">
              {editingItem ? 'Update Item' : 'Add to Itinerary'}
            </Button>
            <Button type="button" variant="outline" onClick={onClose} className="rounded-xl">
              Cancel
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};
