import { useState, useEffect } from 'react';
import { useParams, useSearchParams, useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { useItinerary } from '@/hooks/useItinerary';
import { getSharedItinerary } from '@/lib/sharedItinerary';
import { TravelHeader } from '@/components/TravelHeader';
import { DayTabs } from '@/components/DayTabs';
import { Timeline } from '@/components/Timeline';
import { AddItineraryModal } from '@/components/AddItineraryModal';
import { EditItineraryModal } from '@/components/EditItineraryModal';
import { FloatingNav } from '@/components/FloatingNav';
import { ProfileDropdown } from '@/components/ProfileDropdown';
import { ArrowLeft, Edit3, Share2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';

export interface ItineraryItem {
  id: string;
  time: string;
  title: string;
  location: string;
  description: string;
  type: 'flight' | 'hotel' | 'activity' | 'restaurant' | 'transport';
  links?: { name: string; url: string }[];
  image?: string;
}

export interface DayData {
  id: string;
  date: string;
  dayName: string;
  items: ItineraryItem[];
}

export interface TravelItinerary {
  id: string;
  title: string;
  startDate: string;
  endDate: string;
  thumbnail?: string;
  days: DayData[];
}

const ItineraryDetail = () => {
  const { id } = useParams<{ id: string }>();
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { user } = useAuth();
  const { 
    itineraries: allItineraries, 
    loading: itinerariesLoading,
    addItineraryItem: addItineraryItemService, 
    updateItineraryItem: updateItineraryItemService,
    deleteItineraryItem: deleteItineraryItemService,
    updateItinerary: updateItineraryService,
    generateShareableLink 
  } = useItinerary();
  const { toast } = useToast();

  const [currentItinerary, setCurrentItinerary] = useState<TravelItinerary | null>(null);
  const [selectedDay, setSelectedDay] = useState('1');
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [editingItem, setEditingItem] = useState<ItineraryItem | null>(null);

  const shareableUrl = searchParams.get('share');
  const isSharedView = !!shareableUrl;
  const isReadOnly = searchParams.get('readonly') === 'true' || isSharedView;

  const selectedDayData = currentItinerary?.days.find(day => day.id === selectedDay);

  // Load itinerary - either from shared URL or from user's itineraries
  useEffect(() => {
    const loadItinerary = async () => {
      if (shareableUrl || id === 'shared') {
        // Load shared itinerary
        try {
          const shareUrl = shareableUrl || searchParams.get('share');
          if (!shareUrl) {
            navigate('/');
            return;
          }
          
          const sharedItinerary = await getSharedItinerary(shareUrl);
          if (sharedItinerary) {
            setCurrentItinerary(sharedItinerary);
            setSelectedDay(sharedItinerary.days[0]?.id || '1');
          } else {
            toast({
              title: "Itinerary not found",
              description: "The shared itinerary could not be found or is no longer available.",
              variant: "destructive",
            });
            navigate('/');
          }
        } catch (error) {
          console.error('Error loading shared itinerary:', error);
          toast({
            title: "Error",
            description: "Failed to load shared itinerary.",
            variant: "destructive",
          });
          navigate('/');
        }
      } else if (id && id !== 'shared') {
        // Load user's itinerary
        if (itinerariesLoading) {
          // Still loading itineraries, wait
          return;
        }
        
        const itinerary = allItineraries.find(it => it.id === id);
        console.log('Looking for itinerary with ID:', id);
        console.log('Available itineraries:', allItineraries.map(it => ({ id: it.id, title: it.title })));
        console.log('Found itinerary:', itinerary);
        
        if (itinerary) {
          setCurrentItinerary(itinerary);
          setSelectedDay(itinerary.days[0]?.id || '1');
        } else {
          // Only show error if we've finished loading and still can't find it
          console.error('Itinerary not found - ID:', id, 'Available IDs:', allItineraries.map(it => it.id));
          toast({
            title: "Itinerary not found",
            description: "The requested itinerary could not be found.",
            variant: "destructive",
          });
          navigate('/itineraries');
        }
      }
    };

    loadItinerary();
  }, [id, shareableUrl, allItineraries, itinerariesLoading, navigate, toast, searchParams]);

  const addItineraryItem = async (dayId: string, item: Omit<ItineraryItem, 'id'>) => {
    if (isReadOnly) return;
    
    try {
      const newItem = await addItineraryItemService(dayId, item);
      
      if (currentItinerary) {
        const updatedItinerary = {
          ...currentItinerary,
          days: currentItinerary.days.map(day => 
            day.id === dayId 
              ? { ...day, items: [...day.items, newItem].sort((a, b) => a.time.localeCompare(b.time)) }
              : day
          )
        };
        setCurrentItinerary(updatedItinerary);
      }
      
      toast({
        title: "Success!",
        description: "Item added to your itinerary.",
      });
    } catch (error) {
      console.error('Error adding item:', error);
      toast({
        title: "Error",
        description: "Failed to add item. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleEditItem = (item: ItineraryItem) => {
    if (isReadOnly) return;
    setEditingItem(item);
    setIsEditModalOpen(true);
  };

  const handleDeleteItem = async (itemId: string) => {
    if (isReadOnly) return;
    
    try {
      await deleteItineraryItemService(itemId);
      
      if (currentItinerary) {
        const updatedItinerary = {
          ...currentItinerary,
          days: currentItinerary.days.map(day => ({
            ...day,
            items: day.items.filter(item => item.id !== itemId)
          }))
        };
        setCurrentItinerary(updatedItinerary);
      }
      
      toast({
        title: "Success!",
        description: "Item deleted from your itinerary.",
      });
    } catch (error) {
      console.error('Error deleting item:', error);
      toast({
        title: "Error",
        description: "Failed to delete item. Please try again.",
        variant: "destructive",
      });
    }
  };

  const updateItineraryItem = async (itemId: string, updates: Partial<ItineraryItem>) => {
    if (isReadOnly) return;
    
    try {
      await updateItineraryItemService(itemId, updates);
      
      if (currentItinerary) {
        const updatedItinerary = {
          ...currentItinerary,
          days: currentItinerary.days.map(day => ({
            ...day,
            items: day.items.map(item => 
              item.id === itemId 
                ? { ...item, ...updates }
                : item
            ).sort((a, b) => a.time.localeCompare(b.time))
          }))
        };
        setCurrentItinerary(updatedItinerary);
      }
      
      toast({
        title: "Success!",
        description: "Item updated successfully.",
      });
    } catch (error) {
      console.error('Error updating item:', error);
      toast({
        title: "Error",
        description: "Failed to update item. Please try again.",
        variant: "destructive",
      });
    }
  };

  const updateItinerary = async (updates: { title?: string; thumbnail?: string }) => {
    if (isReadOnly || !currentItinerary) return;
    
    try {
      await updateItineraryService(currentItinerary.id, updates);
      setCurrentItinerary({ ...currentItinerary, ...updates });
      
      toast({
        title: "Success!",
        description: "Itinerary updated successfully.",
      });
    } catch (error) {
      console.error('Error updating itinerary:', error);
      toast({
        title: "Error",
        description: "Failed to update itinerary. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleShareItinerary = async () => {
    if (!currentItinerary) return;
    
    try {
      const shareableUrl = await generateShareableLink(currentItinerary.id);
      const fullUrl = `${window.location.origin}/itinerary/${currentItinerary.id}?share=${shareableUrl}&readonly=true`;
      
      await navigator.clipboard.writeText(fullUrl);
      toast({
        title: "Link copied!",
        description: "Share link has been copied to clipboard.",
      });
    } catch (error) {
      console.error('Error sharing itinerary:', error);
      toast({
        title: "Error",
        description: "Failed to generate share link.",
        variant: "destructive",
      });
    }
  };

  if (!currentItinerary) {
    return (
      <div className="min-h-screen bg-background font-jakarta">
        <TravelHeader showThemeToggle={false} />
        <div className="container mx-auto px-4 py-16 text-center">
          <p className="text-xl text-muted-foreground">
            {itinerariesLoading ? 'Loading itinerary...' : 'Itinerary not found'}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background font-jakarta relative">
      <TravelHeader
        title={currentItinerary.title}
        thumbnail={currentItinerary.thumbnail}
        showThemeToggle={false}
      />
      
      <div className="absolute top-4 left-4 z-50">
        <Button
          variant="outline"
          size="sm"
          onClick={() => isSharedView ? navigate('/') : navigate('/itineraries')}
          className="bg-white/80 hover:bg-white text-gray-800"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back
        </Button>
      </div>

      <div className="absolute top-4 right-4 z-50 flex items-center space-x-2">
        <Button
          variant="outline"
          size="sm"
          onClick={handleShareItinerary}
          className="bg-white/80 hover:bg-white text-gray-800"
        >
          <Share2 className="w-4 h-4 mr-2" />
          Share
        </Button>
        {!isReadOnly && (
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsEditModalOpen(true)}
            className="bg-white/80 hover:bg-white text-gray-800"
          >
            <Edit3 className="w-4 h-4 mr-2" />
            Edit
          </Button>
        )}
        {!isSharedView && <ProfileDropdown />}
      </div>

      <div className="pt-32 pb-24">
        <div className="max-w-4xl mx-auto px-4">
          <DayTabs
            days={currentItinerary.days}
            selectedDay={selectedDay}
            onSelectDay={setSelectedDay}
          />

          <div className="mt-8">
            <Timeline
              day={selectedDayData}
              onEditItem={handleEditItem}
              onDeleteItem={handleDeleteItem}
              isReadOnly={isReadOnly}
            />
          </div>
        </div>
      </div>

      {!isReadOnly && (
        <FloatingNav
          onAddItem={() => setIsAddModalOpen(true)}
        />
      )}

      <AddItineraryModal
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        onAdd={(item) => addItineraryItem(selectedDay, item)}
      />

      <EditItineraryModal
        isOpen={isEditModalOpen}
        onClose={() => {
          setIsEditModalOpen(false);
          setEditingItem(null);
        }}
        itinerary={currentItinerary}
        editingItem={editingItem}
        onUpdateItinerary={updateItinerary}
        onUpdateItem={updateItineraryItem}
      />
    </div>
  );
};

export default ItineraryDetail;
