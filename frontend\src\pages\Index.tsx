import { useEffect } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { TravelHeader } from '@/components/TravelHeader';
import { AuthModal } from '@/components/AuthModal';
import { ThemeToggle } from '@/components/ThemeToggle';
import { MapPin } from 'lucide-react';

const Index = () => {
  const { user } = useAuth();
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  
  const shareableUrl = searchParams.get('share');
  const isSharedView = !!shareableUrl;

  useEffect(() => {
    if (isSharedView) {
      // If it's a shared view, redirect to ItineraryDetail with share params
      navigate(`/itinerary/shared?${searchParams.toString()}`);
    } else if (user) {
      // If user is logged in, redirect to itineraries list
      navigate('/itineraries');
    }
    // If no user and no shared view, stay on the landing page
  }, [user, isSharedView, navigate, searchParams]);

  // Landing page for non-authenticated users
  return (
    <div className="min-h-screen bg-background font-jakarta">
      <TravelHeader />

      <div className="absolute top-4 right-4 z-50 flex items-center space-x-2">
        <ThemeToggle />
      </div>
      
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-2xl mx-auto text-center">
          <div className="mb-8">
            <h1 className="text-5xl font-bold text-foreground mb-4">
              Welcome to TravelPlan
            </h1>
            <p className="text-xl text-muted-foreground">
              Your smart travel companion for creating beautiful itineraries
            </p>
          </div>
          
          <div className="bg-card border border-border rounded-3xl p-8 shadow-lg">
            <div className="w-24 h-24 bg-primary rounded-full flex items-center justify-center mx-auto mb-6">
              <MapPin className="w-12 h-12 text-primary-foreground" />
            </div>
            
            <p className="text-muted-foreground mb-6">
              Please sign in to start creating and managing your travel itineraries.
            </p>
            
            <AuthModal />
          </div>
        </div>
      </div>
    </div>
  );
};

export default Index;
