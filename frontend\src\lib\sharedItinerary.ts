import { TravelItinerary } from '@/pages/Index';
import { apiService } from './api';

export const getSharedItinerary = async (shareableUrl: string): Promise<TravelItinerary | null> => {
  try {
    const response = await apiService.getSharedItinerary(shareableUrl);
    
    if (response.error) {
      console.error('Error fetching shared itinerary:', response.error);
      return null;
    }
    
    return response.data || null;
  } catch (err) {
    console.error('Error fetching shared itinerary:', err);
    return null;
  }
};
