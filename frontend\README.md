# Itinerary Stellar Flow - Frontend

Frontend application built with React + TypeScript + Vite + Tailwind CSS + shadcn/ui.

## Deployment on Netlify

### Environment Variables
Set these in Netlify dashboard:
- `VITE_SUPABASE_URL` - Your Supabase project URL
- `VITE_SUPABASE_ANON_KEY` - Your Supabase anonymous key

### Build Settings
- Build command: `npm run build`
- Publish directory: `dist`
- Node version: 18.x

## Local Development
```bash
npm install
npm run dev
```

## Available Scripts
- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint
