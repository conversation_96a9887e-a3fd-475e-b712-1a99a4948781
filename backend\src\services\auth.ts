import { supabase } from '../config/supabase';
import { Database } from '../types/database';

export interface SignUpData {
  email: string;
  password: string;
  fullName?: string;
}

export interface SignInData {
  email: string;
  password: string;
}

export interface AuthUser {
  id: string;
  email: string;
  fullName?: string;
  avatarUrl?: string;
}

export class AuthService {
  async signUp(data: SignUpData) {
    const { email, password, fullName } = data;
    
    const { data: authData, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          full_name: fullName || '',
        },
      },
    });

    if (error) {
      throw new Error(error.message);
    }

    return authData;
  }

  async signIn(data: SignInData) {
    const { email, password } = data;
    
    const { data: authData, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    if (error) {
      throw new Error(error.message);
    }

    return authData;
  }

  async signOut() {
    const { error } = await supabase.auth.signOut();
    
    if (error) {
      throw new Error(error.message);
    }
  }

  async getCurrentUser(token?: string): Promise<AuthUser | null> {
    let user;
    
    if (token) {
      // Use provided token
      const { data: userData, error } = await supabase.auth.getUser(token);
      if (error || !userData.user) {
        return null;
      }
      user = userData.user;
    } else {
      // Use current session
      const { data: { user: sessionUser }, error } = await supabase.auth.getUser();
      if (error || !sessionUser) {
        return null;
      }
      user = sessionUser;
    }

    // Get user profile data
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', user.id)
      .single();

    if (profileError) {
      console.error('Error fetching user profile:', profileError);
      // Return basic user info if profile fetch fails
      return {
        id: user.id,
        email: user.email || '',
      };
    }

    return {
      id: user.id,
      email: user.email || '',
      fullName: profile?.full_name || undefined,
      avatarUrl: profile?.avatar_url || undefined,
    };
  }

  async updateProfile(userId: string, updates: Partial<{ fullName: string; avatarUrl: string }>) {
    const { data, error } = await supabase
      .from('profiles')
      .update({
        full_name: updates.fullName,
        avatar_url: updates.avatarUrl,
      })
      .eq('id', userId)
      .select()
      .single();

    if (error) {
      throw new Error(error.message);
    }

    return data;
  }

  async resetPassword(email: string, redirectUrl?: string) {
    const { error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: redirectUrl || 'https://makemyitinerary.netlify.app/reset-password',
    });

    if (error) {
      throw new Error(error.message);
    }
  }

  async updatePassword(newPassword: string) {
    const { error } = await supabase.auth.updateUser({
      password: newPassword,
    });

    if (error) {
      throw new Error(error.message);
    }
  }

  // Listen to auth state changes
  onAuthStateChange(callback: (event: string, session: any) => void) {
    return supabase.auth.onAuthStateChange(callback);
  }
}

export const authService = new AuthService();
