const API_BASE_URL = 'https://itinerary-stellar-flow.vercel.app';

// API Response types
export interface ApiResponse<T = any> {
  data?: T;
  error?: string;
  message?: string;
}

// Auth types
export interface SignUpData {
  email: string;
  password: string;
  fullName?: string;
}

export interface SignInData {
  email: string;
  password: string;
}

export interface AuthUser {
  id: string;
  email: string;
  fullName?: string;
}

// Itinerary types
export interface CreateItineraryData {
  title: string;
  startDate: string;
  endDate: string;
  thumbnail?: string;
}

// API Service class
class ApiService {
  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    try {
      const url = `${API_BASE_URL}${endpoint}`;
      const token = localStorage.getItem('auth_token');
      
      const response = await fetch(url, {
        headers: {
          'Content-Type': 'application/json',
          ...(token && { Authorization: `Bearer ${token}` }),
          ...options.headers,
        },
        ...options,
      });

      const responseData = await response.json();

      if (!response.ok) {
        throw new Error(responseData.error || `HTTP ${response.status}`);
      }

      // Handle the new backend response format
      if (responseData.success) {
        return { data: responseData.data };
      } else {
        return { data: responseData };
      }
    } catch (error) {
      console.error(`API Error [${endpoint}]:`, error);
      return { 
        error: error instanceof Error ? error.message : 'An unknown error occurred' 
      };
    }
  }

  // Auth methods
  async signUp(userData: SignUpData): Promise<ApiResponse<AuthUser>> {
    return this.request<AuthUser>('/auth/signup', {
      method: 'POST',
      body: JSON.stringify(userData),
    });
  }

  async signIn(credentials: SignInData): Promise<ApiResponse<{ user: AuthUser; session: any }>> {
    const response = await this.request<{ user: AuthUser; session: any }>('/auth/signin', {
      method: 'POST',
      body: JSON.stringify(credentials),
    });

    // Store session token if sign in successful
    if (response.data?.session?.access_token) {
      localStorage.setItem('auth_token', response.data.session.access_token);
    }

    return response;
  }

  async signOut(): Promise<ApiResponse> {
    const response = await this.request('/auth/signout', {
      method: 'POST',
    });

    // Clear token on sign out
    localStorage.removeItem('auth_token');
    return response;
  }

  async resetPassword(email: string): Promise<ApiResponse> {
    return this.request('/auth/reset-password', {
      method: 'POST',
      body: JSON.stringify({ email }),
    });
  }

  // Transform backend data from snake_case to camelCase
  private transformItinerary(itinerary: any): any {
    if (!itinerary) return null;
    
    return {
      id: itinerary.id,
      title: itinerary.title,
      startDate: itinerary.start_date,
      endDate: itinerary.end_date,
      thumbnail: itinerary.thumbnail,
      days: itinerary.days ? itinerary.days.map((day: any) => ({
        id: day.id,
        date: day.date,
        dayName: day.day_name,
        items: day.itinerary_items ? day.itinerary_items.map((item: any) => this.transformItem(item)).sort((a: any, b: any) => a.time.localeCompare(b.time)) : []
      })) : []
    };
  }

  private transformItem(item: any): any {
    if (!item) return null;
    
    return {
      id: item.id,
      time: item.time,
      title: item.title,
      location: item.location,
      description: item.description,
      type: item.type,
      links: item.links,
      image: item.image
    };
  }

  // Itinerary methods
  async getItineraries(): Promise<ApiResponse<any[]>> {
    const response = await this.request<any[]>('/itinerary');
    if (response.data) {
      response.data = response.data.map(itinerary => this.transformItinerary(itinerary));
    }
    return response;
  }

  async createItinerary(data: CreateItineraryData): Promise<ApiResponse<any>> {
    const response = await this.request<any>('/itinerary', {
      method: 'POST',
      body: JSON.stringify(data),
    });
    if (response.data) {
      response.data = this.transformItinerary(response.data);
    }
    return response;
  }

  async updateItinerary(id: string, data: Partial<CreateItineraryData>): Promise<ApiResponse<any>> {
    return this.request<any>(`/itinerary/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  async deleteItinerary(id: string): Promise<ApiResponse> {
    return this.request(`/itinerary/${id}`, {
      method: 'DELETE',
    });
  }

  async getSharedItinerary(shareableUrl: string): Promise<ApiResponse<any>> {
    const response = await this.request<any>(`/itinerary/shared/${shareableUrl}`);
    if (response.data) {
      response.data = this.transformItinerary(response.data);
    }
    return response;
  }

  async generateShareableLink(itineraryId: string): Promise<ApiResponse<{ shareableUrl: string }>> {
    return this.request<{ shareableUrl: string }>(`/itinerary/${itineraryId}/share`, {
      method: 'POST',
    });
  }

  async addItineraryItem(dayId: string, item: any): Promise<ApiResponse<any>> {
    const response = await this.request<any>(`/itinerary/${dayId}/items`, {
      method: 'POST',
      body: JSON.stringify(item),
    });
    if (response.data) {
      response.data = this.transformItem(response.data);
    }
    return response;
  }

  async updateItineraryItem(itemId: string, updates: any): Promise<ApiResponse<any>> {
    const response = await this.request<any>(`/items/${itemId}`, {
      method: 'PUT',
      body: JSON.stringify(updates),
    });
    if (response.data) {
      response.data = this.transformItem(response.data);
    }
    return response;
  }

  async deleteItineraryItem(itemId: string): Promise<ApiResponse> {
    return this.request(`/items/${itemId}`, {
      method: 'DELETE',
    });
  }

  // Check auth status
  async checkAuth(): Promise<ApiResponse<AuthUser>> {
    return this.request<AuthUser>('/auth/me');
  }
}

export const apiService = new ApiService();
