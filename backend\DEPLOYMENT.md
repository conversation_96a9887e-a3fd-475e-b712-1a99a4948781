# Backend Deployment Guide

## Changes Made to Fix ES Module Issue

### 1. Module System Configuration
- ✅ Removed `"type": "module"` from package.json
- ✅ Updated TypeScript config to use CommonJS modules
- ✅ Fixed all import statements to remove `.js` extensions

### 2. Environment Variables
The backend now supports both naming conventions:
- `SUPABASE_URL` or `VITE_SUPABASE_URL`
- `SUPABASE_ANON_KEY` or `VITE_SUPABASE_ANON_KEY`

### 3. Vercel Configuration
- ✅ Updated vercel.json with proper build configuration
- ✅ Added explicit Node.js 18.x runtime
- ✅ Configured to include all source files

## Deployment Steps

### 1. Set Environment Variables in Vercel Dashboard
Go to your Vercel project settings and add:
```
SUPABASE_URL=https://uvlkygghepjydwmbqdid.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV2bGt5Z2doZXBqeWR3bWJxZGlkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI4MzE4ODAsImV4cCI6MjA2ODQwNzg4MH0.CtfGX9X23-HjMWhtISSWKcKSIwZXq70j-FdQnL94gXw
```

### 2. Deploy
```bash
# From the backend directory
vercel --prod
```

### 3. Test Endpoints
After deployment, test these endpoints:
- `GET /` - API info
- `GET /api/test` - Simple test endpoint
- `POST /auth/signup` - User registration
- `POST /auth/signin` - User login
- `GET /itinerary` - Get user itineraries
- `POST /itinerary` - Create new itinerary

## Troubleshooting

If you still get ES module errors:
1. Check that environment variables are set correctly in Vercel
2. Verify the deployment is using the latest code
3. Check Vercel function logs for detailed error messages

## API Endpoints

### Authentication
- `POST /auth/signup` - Register new user
- `POST /auth/signin` - Login user
- `POST /auth/signout` - Logout user
- `GET /auth/me` - Get current user (placeholder)

### Itineraries
- `GET /itinerary` - Get user's itineraries
- `POST /itinerary` - Create new itinerary

All endpoints support CORS and handle OPTIONS preflight requests.