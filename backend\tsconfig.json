{"compilerOptions": {"target": "ES2020", "module": "CommonJS", "moduleResolution": "node", "outDir": "./dist", "rootDir": ".", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "declaration": true, "declarationMap": true, "sourceMap": true, "types": ["node"], "allowSyntheticDefaultImports": true}, "include": ["src/**/*", "api/**/*"], "exclude": ["node_modules", "dist"]}