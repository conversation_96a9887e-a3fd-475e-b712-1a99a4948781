import { VercelRequest, VercelResponse } from '@vercel/node';
import { authService } from '../src/services/auth';
import { itineraryService } from '../src/services/itinerary';
import { supabase } from '../src/config/supabase';

// Helper function to extract user ID and token from JWT token
async function getUserFromToken(req: VercelRequest): Promise<{ userId: string; token: string } | null> {
  try {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return null;
    }

    const token = authHeader.substring(7); // Remove 'Bearer ' prefix
    
    // Use Supabase to verify the JWT token
    const { data: { user }, error } = await supabase.auth.getUser(token);
    
    if (error || !user) {
      console.error('Token verification failed:', error);
      return null;
    }

    return { userId: user.id, token };
  } catch (error) {
    console.error('Error extracting user from token:', error);
    return null;
  }
}

export default async function handler(req: VercelRequest, res: VercelResponse) {
  console.log('API Request:', { method: req.method, url: req.url, headers: req.headers });

  // Enable CORS for all origins during development
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');
  res.setHeader('Access-Control-Max-Age', '86400');

  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    console.log('Handling OPTIONS request');
    return res.status(200).end();
  }

  const { pathname } = new URL(req.url || '', `http://${req.headers.host}`);
  console.log('Parsed pathname:', pathname);

  try {
    // Route handling
    if (pathname === '/') {
      return res.json({ 
        message: 'Itinerary Stellar Flow API', 
        version: '1.0.0',
        endpoints: {
          auth: ['/auth/signup', '/auth/signin', '/auth/signout'],
          itinerary: ['/itinerary']
        }
      });
    } else if (pathname.startsWith('/auth')) {
      return handleAuth(req, res);
    } else if (pathname.startsWith('/itinerary')) {
      return handleItinerary(req, res);
    } else {
      return res.status(404).json({ error: 'Not found' });
    }
  } catch (error) {
    console.error('API Error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}

async function handleAuth(req: VercelRequest, res: VercelResponse) {
  const { pathname } = new URL(req.url || '', `http://${req.headers.host}`);
  
  try {
    if (pathname === '/auth/signup' && req.method === 'POST') {
      const result = await authService.signUp(req.body);
      return res.json({ success: true, data: result });
    } else if (pathname === '/auth/signin' && req.method === 'POST') {
      const result = await authService.signIn(req.body);
      return res.json({ success: true, data: result });
    } else if (pathname === '/auth/signout' && req.method === 'POST') {
      await authService.signOut();
      return res.json({ success: true });
    } else if (pathname === '/auth/me' && req.method === 'GET') {
      const authHeader = req.headers.authorization;
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return res.status(401).json({ error: 'Authentication required' });
      }
      
      const token = authHeader.substring(7);
      const user = await authService.getCurrentUser(token);
      
      if (!user) {
        return res.status(401).json({ error: 'Invalid token' });
      }
      
      return res.json({ success: true, data: user });
    } else {
      return res.status(404).json({ error: 'Auth endpoint not found' });
    }
  } catch (error) {
    console.error('Auth error:', error);
    return res.status(500).json({ 
      error: error instanceof Error ? error.message : 'Authentication failed' 
    });
  }
}

async function handleItinerary(req: VercelRequest, res: VercelResponse) {
  const { pathname } = new URL(req.url || '', `http://${req.headers.host}`);
  
  try {
    // Handle shared itineraries (no authentication needed)
    if (pathname.startsWith('/itinerary/shared/') && req.method === 'GET') {
      const shareableUrl = pathname.split('/').pop();
      if (!shareableUrl) {
        return res.status(400).json({ error: 'Shareable URL required' });
      }
      
      const result = await itineraryService.getSharedItinerary(shareableUrl);
      if (!result) {
        return res.status(404).json({ error: 'Shared itinerary not found' });
      }
      
      return res.json({ success: true, data: result });
    }

    // Extract user ID from JWT token for authenticated endpoints
    const authData = await getUserFromToken(req);
    
    if (!authData) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const { userId, token } = authData;

    // Parse URL to extract IDs
    const pathParts = pathname.split('/').filter(Boolean);

    if (pathname === '/itinerary' && req.method === 'POST') {
      const result = await itineraryService.createItinerary(userId, req.body);
      return res.json({ success: true, data: result });
    } else if (pathname === '/itinerary' && req.method === 'GET') {
      const result = await itineraryService.getUserItineraries(userId);
      return res.json({ success: true, data: result });
    } else if (pathParts.length === 2 && pathParts[0] === 'itinerary' && req.method === 'PUT') {
      // PUT /itinerary/{id}
      const itineraryId = pathParts[1];
      const result = await itineraryService.updateItinerary(itineraryId, req.body);
      return res.json({ success: true, data: result });
    } else if (pathParts.length === 2 && pathParts[0] === 'itinerary' && req.method === 'DELETE') {
      // DELETE /itinerary/{id}
      const itineraryId = pathParts[1];
      await itineraryService.deleteItinerary(itineraryId);
      return res.json({ success: true });
    } else if (pathParts.length === 3 && pathParts[0] === 'itinerary' && pathParts[2] === 'share' && req.method === 'POST') {
      // POST /itinerary/{id}/share
      const itineraryId = pathParts[1];
      const shareableUrl = await itineraryService.generateShareableLink(itineraryId);
      return res.json({ success: true, data: { shareableUrl } });
    } else if (pathParts.length === 3 && pathParts[0] === 'itinerary' && pathParts[2] === 'items' && req.method === 'POST') {
      // POST /itinerary/{dayId}/items
      const dayId = pathParts[1];
      const itemData = { ...req.body, dayId };
      const result = await itineraryService.createItineraryItem(itemData, token);
      return res.json({ success: true, data: result });
    } else if (pathParts.length === 2 && pathParts[0] === 'items' && req.method === 'PUT') {
      // PUT /items/{id}
      const itemId = pathParts[1];
      const result = await itineraryService.updateItineraryItem(itemId, req.body, token);
      return res.json({ success: true, data: result });
    } else if (pathParts.length === 2 && pathParts[0] === 'items' && req.method === 'DELETE') {
      // DELETE /items/{id}
      const itemId = pathParts[1];
      await itineraryService.deleteItineraryItem(itemId, token);
      return res.json({ success: true });
    } else {
      return res.status(404).json({ error: 'Itinerary endpoint not found' });
    }
  } catch (error) {
    console.error('Itinerary error:', error);
    return res.status(500).json({ 
      error: error instanceof Error ? error.message : 'Itinerary operation failed' 
    });
  }
}
