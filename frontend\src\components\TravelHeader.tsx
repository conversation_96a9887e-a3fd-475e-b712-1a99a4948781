
import { Plane, ArrowLeft, Share2, Edit3 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { ThemeToggle } from '@/components/ThemeToggle';

interface TravelHeaderProps {
  title?: string;
  destination?: string;
  thumbnail?: string;
  onBack?: () => void;
  onShare?: () => void;
  onEditItinerary?: () => void;
  isReadOnly?: boolean;
}

export const TravelHeader = ({ title, destination, thumbnail, onBack, onShare, onEditItinerary, isReadOnly }: TravelHeaderProps) => {
  return (
    <header className="bg-background/95 backdrop-blur-md border-b border-border sticky top-0 z-50">
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            {onBack && (
              <Button variant="ghost" size="sm" onClick={onBack} className="mr-2">
                <ArrowLeft className="w-4 h-4" />
              </Button>
            )}
            {thumbnail ? (
              <div className="w-12 h-12 rounded-xl overflow-hidden">
                <img src={thumbnail} alt={title} className="w-full h-full object-cover" />
              </div>
            ) : (
              <div className="w-12 h-12 bg-primary rounded-xl flex items-center justify-center">
                <Plane className="w-6 h-6 text-primary-foreground" />
              </div>
            )}
            <div>
              <h2 className="text-xl font-bold text-foreground">
                {title || 'Make My Itinerary'}
              </h2>
              <p className="text-sm text-muted-foreground">
                {destination || 'Your Journey, Organized'}
              </p>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <ThemeToggle />
            {onShare && (
              <>
                {!isReadOnly && onEditItinerary && (
                  <Button variant="outline" size="sm" className="mr-2" onClick={onEditItinerary}>
                    <Edit3 className="w-4 h-4 mr-2" />
                    Edit Itinerary
                  </Button>
                )}
                <Button variant="outline" size="sm" onClick={onShare}>
                  <Share2 className="w-4 h-4 mr-2" />
                  Share
                </Button>
              </>
            )}
          </div>
        </div>
      </div>
    </header>
  );
};
