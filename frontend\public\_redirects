# Static assets should not be redirected - serve them directly
/assets/*           /assets/:splat                                    200
/manifest.json      /manifest.json                                    200
/sw.js              /sw.js                                            200
/favicon.ico        /favicon.ico                                      200
/favicon.svg        /favicon.svg                                      200
/itinerary.svg      /itinerary.svg                                    200
/robots.txt         /robots.txt                                       200
/*.js               /:splat                                           200
/*.css              /:splat                                           200
/*.png              /:splat                                           200
/*.jpg              /:splat                                           200
/*.jpeg             /:splat                                           200
/*.svg              /:splat                                           200
/*.ico              /:splat                                           200
/*.woff             /:splat                                           200
/*.woff2            /:splat                                           200

# API proxy (optional - if you want to proxy API calls through Netlify)
/api/*              https://itinerary-stellar-flow.vercel.app/:splat  200

# SPA fallback for all other routes - this must be last
/*                  /index.html                                       200
