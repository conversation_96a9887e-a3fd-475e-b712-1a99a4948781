export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string;
          email: string;
          full_name: string | null;
          avatar_url: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id: string;
          email: string;
          full_name?: string | null;
          avatar_url?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          email?: string;
          full_name?: string | null;
          avatar_url?: string | null;
          created_at?: string;
          updated_at?: string;
        };
      };
      itineraries: {
        Row: {
          id: string;
          user_id: string;
          title: string;
          start_date: string;
          end_date: string;
          thumbnail: string | null;
          shared_url: string | null;
          is_public: boolean;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          title: string;
          start_date: string;
          end_date: string;
          thumbnail?: string | null;
          shared_url?: string | null;
          is_public?: boolean;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          title?: string;
          start_date?: string;
          end_date?: string;
          thumbnail?: string | null;
          shared_url?: string | null;
          is_public?: boolean;
          created_at?: string;
          updated_at?: string;
        };
      };
      days: {
        Row: {
          id: string;
          itinerary_id: string;
          date: string;
          day_name: string;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          itinerary_id: string;
          date: string;
          day_name: string;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          itinerary_id?: string;
          date?: string;
          day_name?: string;
          created_at?: string;
          updated_at?: string;
        };
      };
      itinerary_items: {
        Row: {
          id: string;
          day_id: string;
          time: string;
          title: string;
          location: string;
          description: string;
          type: 'flight' | 'hotel' | 'activity' | 'restaurant' | 'transport';
          links: { name: string; url: string }[] | null;
          image: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          day_id: string;
          time: string;
          title: string;
          location: string;
          description: string;
          type: 'flight' | 'hotel' | 'activity' | 'restaurant' | 'transport';
          links?: { name: string; url: string }[] | null;
          image?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          day_id?: string;
          time?: string;
          title?: string;
          location?: string;
          description?: string;
          type?: 'flight' | 'hotel' | 'activity' | 'restaurant' | 'transport';
          links?: { name: string; url: string }[] | null;
          image?: string | null;
          created_at?: string;
          updated_at?: string;
        };
      };
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      [_ in never]: never;
    };
    Enums: {
      item_type: 'flight' | 'hotel' | 'activity' | 'restaurant' | 'transport';
    };
    CompositeTypes: {
      [_ in never]: never;
    };
  };
}
