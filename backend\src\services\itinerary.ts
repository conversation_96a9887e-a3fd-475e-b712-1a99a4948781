import { supabase } from '../config/supabase';
import { Database } from '../types/database';
import { createClient } from '@supabase/supabase-js';

type Tables = Database['public']['Tables'];
type Itinerary = Tables['itineraries']['Row'];
type Day = Tables['days']['Row'];
type ItineraryItem = Tables['itinerary_items']['Row'];

export interface CreateItineraryData {
  title: string;
  startDate: string;
  endDate: string;
  thumbnail?: string;
}

export interface CreateDayData {
  itineraryId: string;
  date: string;
  dayName: string;
}

export interface CreateItineraryItemData {
  dayId: string;
  time: string;
  title: string;
  location: string;
  description: string;
  type: 'flight' | 'hotel' | 'activity' | 'restaurant' | 'transport';
  links?: { name: string; url: string }[];
  image?: string;
}

export interface ItineraryWithDays extends Itinerary {
  days: (Day & {
    items: ItineraryItem[];
  })[];
}

export class ItineraryService {
  // Generate a unique shareable URL
  private generateShareableUrl(): string {
    const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = '';
    for (let i = 0; i < 12; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  // Create an authenticated Supabase client with the provided token
  private getAuthenticatedClient(token: string) {
    const supabaseUrl = process.env.SUPABASE_URL;
    const supabaseKey = process.env.SUPABASE_ANON_KEY;
    
    if (!supabaseUrl || !supabaseKey) {
      console.error('Missing Supabase configuration:', { supabaseUrl: !!supabaseUrl, supabaseKey: !!supabaseKey });
      throw new Error('Missing Supabase configuration');
    }

    console.log('Creating authenticated client with token');

    const client = createClient(supabaseUrl, supabaseKey, {
      global: {
        headers: {
          Authorization: `Bearer ${token}`
        }
      }
    });

    return client;
  }

  async getUserItineraries(userId: string): Promise<ItineraryWithDays[]> {
    const { data, error } = await supabase
      .from('itineraries')
      .select(`
        *,
        days (
          *,
          itinerary_items (*)
        )
      `)
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (error) {
      throw new Error(error.message);
    }

    return data as ItineraryWithDays[];
  }

  async getItinerary(id: string): Promise<ItineraryWithDays | null> {
    const { data, error } = await supabase
      .from('itineraries')
      .select(`
        *,
        days (
          *,
          itinerary_items (*)
        )
      `)
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return null; // No rows found
      }
      throw new Error(error.message);
    }

    return data as ItineraryWithDays;
  }

  async createItinerary(userId: string, data: CreateItineraryData): Promise<ItineraryWithDays> {
    // Create itinerary
    const { data: itinerary, error: itineraryError } = await supabase
      .from('itineraries')
      .insert({
        user_id: userId,
        title: data.title,
        start_date: data.startDate,
        end_date: data.endDate,
        thumbnail: data.thumbnail,
      })
      .select()
      .single();

    if (itineraryError) {
      throw new Error(itineraryError.message);
    }

    // Create days
    const start = new Date(data.startDate);
    const end = new Date(data.endDate);
    const dayCount = Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24)) + 1;

    const daysData = Array.from({ length: dayCount }, (_, index) => {
      const currentDate = new Date(start);
      currentDate.setDate(start.getDate() + index);
      
      return {
        itinerary_id: itinerary.id,
        date: currentDate.toISOString().split('T')[0],
        day_name: `Day ${index + 1}`,
      };
    });

    const { data: days, error: daysError } = await supabase
      .from('days')
      .insert(daysData)
      .select();

    if (daysError) {
      throw new Error(daysError.message);
    }

    return {
      ...itinerary,
      days: days.map(day => ({ ...day, items: [] })),
    };
  }

  async updateItinerary(id: string, updates: Partial<CreateItineraryData>): Promise<Itinerary> {
    const { data, error } = await supabase
      .from('itineraries')
      .update({
        title: updates.title,
        start_date: updates.startDate,
        end_date: updates.endDate,
        thumbnail: updates.thumbnail,
      })
      .eq('id', id)
      .select()
      .single();

    if (error) {
      throw new Error(error.message);
    }

    return data;
  }

  async deleteItinerary(id: string): Promise<void> {
    const { error } = await supabase
      .from('itineraries')
      .delete()
      .eq('id', id);

    if (error) {
      throw new Error(error.message);
    }
  }

  async createItineraryItem(data: CreateItineraryItemData, token: string): Promise<ItineraryItem> {
    const authenticatedClient = this.getAuthenticatedClient(token);
    
    const { data: item, error } = await authenticatedClient
      .from('itinerary_items')
      .insert({
        day_id: data.dayId,
        time: data.time,
        title: data.title,
        location: data.location,
        description: data.description,
        type: data.type,
        links: data.links || null,
        image: data.image || null,
      })
      .select()
      .single();

    if (error) {
      throw new Error(error.message);
    }

    return item;
  }

  async updateItineraryItem(id: string, updates: Partial<CreateItineraryItemData>, token: string): Promise<ItineraryItem> {
    const authenticatedClient = this.getAuthenticatedClient(token);
    
    const { data, error } = await authenticatedClient
      .from('itinerary_items')
      .update({
        time: updates.time,
        title: updates.title,
        location: updates.location,
        description: updates.description,
        type: updates.type,
        links: updates.links || null,
        image: updates.image || null,
      })
      .eq('id', id)
      .select()
      .single();

    if (error) {
      throw new Error(error.message);
    }

    return data;
  }

  async deleteItineraryItem(id: string, token: string): Promise<void> {
    const authenticatedClient = this.getAuthenticatedClient(token);
    
    const { error } = await authenticatedClient
      .from('itinerary_items')
      .delete()
      .eq('id', id);

    if (error) {
      throw new Error(error.message);
    }
  }

  async getDayItems(dayId: string): Promise<ItineraryItem[]> {
    const { data, error } = await supabase
      .from('itinerary_items')
      .select('*')
      .eq('day_id', dayId)
      .order('time', { ascending: true });

    if (error) {
      throw new Error(error.message);
    }

    return data;
  }

  async generateShareableLink(itineraryId: string): Promise<string> {
    const shareableUrl = this.generateShareableUrl();
    
    const { error } = await supabase
      .from('itineraries')
      .update({
        shared_url: shareableUrl,
        is_public: true
      })
      .eq('id', itineraryId);

    if (error) {
      throw new Error(error.message);
    }

    return shareableUrl;
  }

  async getSharedItinerary(shareableUrl: string): Promise<ItineraryWithDays | null> {
    console.log('Looking for shared itinerary with URL:', shareableUrl);
    
    // First, let's check if any itinerary exists with this shared URL
    const { data: checkData, error: checkError } = await supabase
      .from('itineraries')
      .select('id, shared_url, is_public')
      .eq('shared_url', shareableUrl);

    console.log('Check query result:', { checkData, checkError });

    const { data, error } = await supabase
      .from('itineraries')
      .select(`
        *,
        days (
          *,
          itinerary_items (*)
        )
      `)
      .eq('shared_url', shareableUrl)
      .eq('is_public', true)
      .single();

    if (error) {
      console.log('Error fetching shared itinerary:', error);
      if (error.code === 'PGRST116') {
        return null; // No rows found
      }
      throw new Error(error.message);
    }

    return data as ItineraryWithDays;
  }

  async disableSharing(itineraryId: string): Promise<void> {
    const { error } = await supabase
      .from('itineraries')
      .update({
        shared_url: null,
        is_public: false
      })
      .eq('id', itineraryId);

    if (error) {
      throw new Error(error.message);
    }
  }
}

export const itineraryService = new ItineraryService();
