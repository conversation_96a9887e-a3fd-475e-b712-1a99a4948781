# Deployment Notes

## Changes Made to Fix Authentication Issue

### Problem
The backend was using `'placeholder-user-id'` instead of extracting the real user ID from JWT tokens, causing database errors like:
```
invalid input syntax for type uuid: "placeholder-user-id"
```

### Solution
1. **Added JWT Token Validation**: Created `getUserFromToken()` function that uses Supabase to verify JWT tokens
2. **Updated Itinerary Endpoints**: Both GET and POST `/itinerary` endpoints now extract real user IDs from tokens
3. **Enhanced Auth Service**: Updated `getCurrentUser()` to accept tokens for server-side validation
4. **Improved `/auth/me` Endpoint**: Now properly validates tokens and returns user data

### Key Changes
- `api/index.ts`: Added JWT token extraction and validation
- `src/services/auth.ts`: Enhanced `getCurrentUser()` method
- All itinerary operations now require valid authentication
- Proper error handling for invalid/missing tokens

### Testing
After deployment, test these scenarios:
1. Sign in through frontend - should receive valid JWT token
2. Access `/itinerary` endpoint - should return user's itineraries (not placeholder error)
3. Create new itinerary - should work with real user ID
4. Access `/auth/me` - should return current user data

### Environment Variables Required
Make sure these are set in Vercel:
- `SUPABASE_URL`
- `SUPABASE_ANON_KEY`