import { Plus, Home, Share2 } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface FloatingNavProps {
  onAddItem: () => void;
  onGoHome: () => void;
  onShare: () => void;
  isReadOnly?: boolean;
}

export const FloatingNav = ({ onAddItem, onGoHome, onShare, isReadOnly }: FloatingNavProps) => {
  return (
    <div className="fixed bottom-6 left-1/2 transform -translate-x-1/2 z-50 md:hidden">
      <div className="bg-background/95 backdrop-blur-md border border-border rounded-full px-4 py-2 shadow-lg">
        <div className="flex items-center gap-2">
          <Button variant="ghost" size="sm" onClick={onGoHome} className="rounded-full h-10 w-10 p-0">
            <Home className="w-4 h-4" />
          </Button>
          
          {!isReadOnly && (
            <Button onClick={onAddItem} className="rounded-full h-10 px-4">
              <Plus className="w-4 h-4 mr-2" />
              Add Item
            </Button>
          )}
          
          <Button variant="ghost" size="sm" onClick={onShare} className="rounded-full h-10 w-10 p-0">
            <Share2 className="w-4 h-4" />
          </Button>
        </div>
      </div>
    </div>
  );
};