@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    /* Travel Itinerary Color Palette */
    --background: 0 0% 100%;
    --foreground: 0 0% 9.4%;

    --card: 0 0% 100%;
    --card-foreground: 0 0% 9.4%;

    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 9.4%;

    --primary: 81 100% 74%;
    --primary-foreground: 0 0% 9.4%;

    --secondary: 0 0% 96%;
    --secondary-foreground: 0 0% 9.4%;

    --muted: 0 0% 96%;
    --muted-foreground: 0 0% 45.1%;

    --accent: 81 100% 74%;
    --accent-foreground: 0 0% 9.4%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 81 100% 74%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 0 0% 9.4%;
    --foreground: 0 0% 100%;

    --card: 0 0% 9.4%;
    --card-foreground: 0 0% 100%;

    --popover: 0 0% 9.4%;
    --popover-foreground: 0 0% 100%;

    --primary: 81 100% 74%;
    --primary-foreground: 0 0% 9.4%;

    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 100%;

    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;

    --accent: 81 100% 74%;
    --accent-foreground: 0 0% 9.4%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 100%;

    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 81 100% 74%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-family: 'Plus Jakarta Sans', sans-serif;
  }

  /* Custom scrollbar styling - minimalist with primary color */
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  ::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 3px;
  }

  ::-webkit-scrollbar-thumb {
    background: hsl(var(--primary) / 0.4);
    border-radius: 3px;
    border: 1px solid hsl(var(--background));
  }

  ::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--primary) / 0.6);
  }

  /* Firefox scrollbar styling */
  * {
    scrollbar-width: thin;
    scrollbar-color: hsl(var(--primary) / 0.4) transparent;
  }
}