<!DOCTYPE html>
<html>
<head>
    <title>Icon Generator</title>
</head>
<body>
    <canvas id="canvas192" width="192" height="192" style="border: 1px solid #ccc; margin: 10px;"></canvas>
    <canvas id="canvas512" width="512" height="512" style="border: 1px solid #ccc; margin: 10px;"></canvas>
    <br>
    <button onclick="downloadIcons()">Download PNG Icons</button>
    
    <script>
        // Function to draw the 192x192 icon
        function draw192Icon() {
            const canvas = document.getElementById('canvas192');
            const ctx = canvas.getContext('2d');
            
            // Clear canvas
            ctx.clearRect(0, 0, 192, 192);
            
            // Background circle
            ctx.beginPath();
            ctx.arc(96, 96, 96, 0, 2 * Math.PI);
            ctx.fillStyle = '#7EDD3C';
            ctx.fill();
            
            // Suitcase
            ctx.fillStyle = 'white';
            ctx.fillRect(68, 88, 56, 36);
            
            // Handle
            ctx.fillRect(76, 72, 40, 4);
            ctx.fillRect(76, 68, 4, 8);
            ctx.fillRect(112, 68, 4, 8);
            
            // Lock
            ctx.fillStyle = '#7EDD3C';
            ctx.fillRect(93, 92, 6, 8);
            
            // Location pin
            ctx.fillStyle = 'white';
            ctx.beginPath();
            ctx.arc(116, 76, 12, 0, 2 * Math.PI);
            ctx.fill();
            
            ctx.fillStyle = '#7EDD3C';
            ctx.beginPath();
            ctx.arc(116, 76, 8, 0, 2 * Math.PI);
            ctx.fill();
            
            ctx.fillStyle = 'white';
            ctx.beginPath();
            ctx.arc(116, 76, 3, 0, 2 * Math.PI);
            ctx.fill();
        }
        
        // Function to draw the 512x512 icon
        function draw512Icon() {
            const canvas = document.getElementById('canvas512');
            const ctx = canvas.getContext('2d');
            
            // Clear canvas
            ctx.clearRect(0, 0, 512, 512);
            
            // Background circle with gradient
            const gradient = ctx.createRadialGradient(256, 150, 0, 256, 256, 400);
            gradient.addColorStop(0, '#8EE53F');
            gradient.addColorStop(1, '#7EDD3C');
            
            ctx.beginPath();
            ctx.arc(256, 256, 256, 0, 2 * Math.PI);
            ctx.fillStyle = gradient;
            ctx.fill();
            
            // Main suitcase
            ctx.fillStyle = 'white';
            ctx.fillRect(181, 236, 150, 96);
            
            // Handle
            ctx.fillRect(202, 192, 108, 12);
            ctx.fillRect(202, 180, 12, 24);
            ctx.fillRect(298, 180, 12, 24);
            
            // Suitcase details
            ctx.fillStyle = 'rgba(126, 221, 60, 0.3)';
            ctx.fillRect(196, 248, 120, 4);
            ctx.fillRect(196, 268, 120, 4);
            ctx.fillRect(196, 288, 120, 4);
            
            // Lock
            ctx.fillStyle = '#7EDD3C';
            ctx.fillRect(248, 248, 16, 20);
            ctx.fillStyle = 'white';
            ctx.beginPath();
            ctx.arc(256, 262, 3, 0, 2 * Math.PI);
            ctx.fill();
            
            // Location pin
            ctx.fillStyle = 'white';
            ctx.beginPath();
            ctx.arc(311, 201, 32, 0, 2 * Math.PI);
            ctx.fill();
            
            ctx.fillStyle = '#7EDD3C';
            ctx.beginPath();
            ctx.arc(311, 201, 22, 0, 2 * Math.PI);
            ctx.fill();
            
            ctx.fillStyle = 'white';
            ctx.beginPath();
            ctx.arc(311, 201, 8, 0, 2 * Math.PI);
            ctx.fill();
            
            // Decorative elements
            ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
            ctx.beginPath();
            ctx.arc(166, 306, 4, 0, 2 * Math.PI);
            ctx.fill();
            
            ctx.beginPath();
            ctx.arc(346, 226, 4, 0, 2 * Math.PI);
            ctx.fill();
        }
        
        function downloadIcons() {
            // Download 192x192
            const canvas192 = document.getElementById('canvas192');
            const link192 = document.createElement('a');
            link192.download = 'icon-192.png';
            link192.href = canvas192.toDataURL();
            link192.click();
            
            // Download 512x512
            const canvas512 = document.getElementById('canvas512');
            const link512 = document.createElement('a');
            link512.download = 'icon-512.png';
            link512.href = canvas512.toDataURL();
            link512.click();
        }
        
        // Draw icons when page loads
        window.onload = function() {
            draw192Icon();
            draw512Icon();
        };
    </script>
</body>
</html>
