import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { apiService } from '@/lib/api';
import { TravelItinerary, DayData, ItineraryItem } from '@/pages/Index';

export const useItinerary = () => {
  const { user } = useAuth();
  const [itineraries, setItineraries] = useState<TravelItinerary[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch all itineraries for the user
  const fetchItineraries = async () => {
    if (!user) return;

    try {
      setLoading(true);
      setError(null);

      const response = await apiService.getItineraries();
      
      if (response.error) {
        throw new Error(response.error);
      }

      if (response.data) {
        setItineraries(Array.isArray(response.data) ? response.data : []);
      }
    } catch (err) {
      console.error('Error fetching itineraries:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch itineraries');
    } finally {
      setLoading(false);
    }
  };

  // Create new itinerary
  const createItinerary = async (title: string, startDate: string, endDate: string, thumbnail?: string) => {
    if (!user) throw new Error('User not authenticated');

    try {
      setLoading(true);
      setError(null);

      const response = await apiService.createItinerary({
        title,
        startDate,
        endDate,
        thumbnail,
      });

      if (response.error) {
        throw new Error(response.error);
      }

      if (response.data) {
        setItineraries(prev => [response.data, ...(Array.isArray(prev) ? prev : [])]);
        return response.data;
      }
    } catch (err) {
      console.error('Error creating itinerary:', err);
      setError(err instanceof Error ? err.message : 'Failed to create itinerary');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Update itinerary
  const updateItinerary = async (id: string, updates: Partial<TravelItinerary>) => {
    if (!user) throw new Error('User not authenticated');

    try {
      setLoading(true);
      setError(null);

      const response = await apiService.updateItinerary(id, {
        title: updates.title,
        startDate: updates.startDate,
        endDate: updates.endDate,
        thumbnail: updates.thumbnail,
      });

      if (response.error) {
        throw new Error(response.error);
      }

      setItineraries(prev => 
        (Array.isArray(prev) ? prev : []).map(itinerary => 
          itinerary.id === id 
            ? { ...itinerary, ...updates }
            : itinerary
        )
      );
    } catch (err) {
      console.error('Error updating itinerary:', err);
      setError(err instanceof Error ? err.message : 'Failed to update itinerary');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Delete itinerary
  const deleteItinerary = async (id: string) => {
    if (!user) throw new Error('User not authenticated');

    try {
      setLoading(true);
      setError(null);

      const response = await apiService.deleteItinerary(id);

      if (response.error) {
        throw new Error(response.error);
      }

      setItineraries(prev => (Array.isArray(prev) ? prev : []).filter(itinerary => itinerary.id !== id));
    } catch (err) {
      console.error('Error deleting itinerary:', err);
      setError(err instanceof Error ? err.message : 'Failed to delete itinerary');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const addItineraryItem = async (dayId: string, item: Omit<ItineraryItem, 'id'>) => {
    if (!user) throw new Error('User not authenticated');

    try {
      setLoading(true);
      setError(null);

      const response = await apiService.addItineraryItem(dayId, item);

      if (response.error) {
        throw new Error(response.error);
      }

      return response.data;
    } catch (err) {
      console.error('Error adding itinerary item:', err);
      setError(err instanceof Error ? err.message : 'Failed to add itinerary item');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const updateItineraryItem = async (itemId: string, updates: Partial<ItineraryItem>) => {
    if (!user) throw new Error('User not authenticated');

    try {
      setLoading(true);
      setError(null);

      const response = await apiService.updateItineraryItem(itemId, updates);

      if (response.error) {
        throw new Error(response.error);
      }

      return response.data;
    } catch (err) {
      console.error('Error updating itinerary item:', err);
      setError(err instanceof Error ? err.message : 'Failed to update itinerary item');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const deleteItineraryItem = async (itemId: string) => {
    if (!user) throw new Error('User not authenticated');

    try {
      setLoading(true);
      setError(null);

      const response = await apiService.deleteItineraryItem(itemId);

      if (response.error) {
        throw new Error(response.error);
      }
    } catch (err) {
      console.error('Error deleting itinerary item:', err);
      setError(err instanceof Error ? err.message : 'Failed to delete itinerary item');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const generateShareableLink = async (itineraryId: string): Promise<string> => {
    if (!user) throw new Error('User not authenticated');

    try {
      setLoading(true);
      setError(null);

      const response = await apiService.generateShareableLink(itineraryId);

      if (response.error) {
        throw new Error(response.error);
      }

      return response.data?.shareableUrl || '';
    } catch (err) {
      console.error('Error generating shareable link:', err);
      setError(err instanceof Error ? err.message : 'Failed to generate shareable link');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (user) {
      fetchItineraries();
    } else {
      setItineraries([]);
      setLoading(false);
    }
  }, [user]);

  return {
    itineraries,
    loading,
    error,
    createItinerary,
    updateItinerary,
    deleteItinerary,
    addItineraryItem,
    updateItineraryItem,
    deleteItineraryItem,
    generateShareableLink,
    refetch: fetchItineraries,
  };
};
