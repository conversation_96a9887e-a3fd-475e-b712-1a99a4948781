-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create enum for item types
CREATE TYPE item_type AS ENUM ('flight', 'hotel', 'activity', 'restaurant', 'transport');

-- Create profiles table
CREATE TABLE profiles (
  id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
  email TEXT UNIQUE NOT NULL,
  full_name TEXT,
  avatar_url TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create itineraries table
CREATE TABLE itineraries (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  title TEXT NOT NULL,
  start_date DATE NOT NULL,
  end_date DATE NOT NULL,
  thumbnail TEXT,
  shared_url TEXT UNIQUE,
  is_public B<PERSON><PERSON>EAN DEFAULT FALSE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create days table
CREATE TABLE days (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  itinerary_id UUID REFERENCES itineraries(id) ON DELETE CASCADE NOT NULL,
  date DATE NOT NULL,
  day_name TEXT NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create itinerary_items table
CREATE TABLE itinerary_items (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  day_id UUID REFERENCES days(id) ON DELETE CASCADE NOT NULL,
  time TIME NOT NULL,
  title TEXT NOT NULL,
  location TEXT NOT NULL,
  description TEXT NOT NULL,
  type item_type NOT NULL,
  links JSONB,
  image TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes
CREATE INDEX idx_itineraries_user_id ON itineraries(user_id);
CREATE INDEX idx_itineraries_shared_url ON itineraries(shared_url);
CREATE INDEX idx_days_itinerary_id ON days(itinerary_id);
CREATE INDEX idx_itinerary_items_day_id ON itinerary_items(day_id);
CREATE INDEX idx_itinerary_items_time ON itinerary_items(time);

-- Enable Row Level Security (RLS)
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE itineraries ENABLE ROW LEVEL SECURITY;
ALTER TABLE days ENABLE ROW LEVEL SECURITY;
ALTER TABLE itinerary_items ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
-- Profiles: Users can only see and update their own profile
CREATE POLICY "Users can view own profile" ON profiles FOR SELECT USING (auth.uid() = id);
CREATE POLICY "Users can update own profile" ON profiles FOR UPDATE USING (auth.uid() = id);
CREATE POLICY "Users can insert own profile" ON profiles FOR INSERT WITH CHECK (auth.uid() = id);

-- Itineraries: Users can only see and manage their own itineraries, but public shared itineraries can be viewed by anyone
CREATE POLICY "Users can view own itineraries" ON itineraries FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Anyone can view public shared itineraries" ON itineraries FOR SELECT USING (is_public = true);
CREATE POLICY "Users can insert own itineraries" ON itineraries FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own itineraries" ON itineraries FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own itineraries" ON itineraries FOR DELETE USING (auth.uid() = user_id);

-- Days: Users can only see and manage days of their own itineraries, but anyone can view days of public shared itineraries
CREATE POLICY "Users can view own days" ON days FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM itineraries 
    WHERE itineraries.id = days.itinerary_id 
    AND itineraries.user_id = auth.uid()
  )
);
CREATE POLICY "Anyone can view days of public shared itineraries" ON days FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM itineraries 
    WHERE itineraries.id = days.itinerary_id 
    AND itineraries.is_public = true
  )
);
CREATE POLICY "Users can insert own days" ON days FOR INSERT WITH CHECK (
  EXISTS (
    SELECT 1 FROM itineraries 
    WHERE itineraries.id = days.itinerary_id 
    AND itineraries.user_id = auth.uid()
  )
);
CREATE POLICY "Users can update own days" ON days FOR UPDATE USING (
  EXISTS (
    SELECT 1 FROM itineraries 
    WHERE itineraries.id = days.itinerary_id 
    AND itineraries.user_id = auth.uid()
  )
);
CREATE POLICY "Users can delete own days" ON days FOR DELETE USING (
  EXISTS (
    SELECT 1 FROM itineraries 
    WHERE itineraries.id = days.itinerary_id 
    AND itineraries.user_id = auth.uid()
  )
);

-- Itinerary Items: Users can only see and manage items of their own itineraries, but anyone can view items of public shared itineraries
CREATE POLICY "Users can view own itinerary items" ON itinerary_items FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM days 
    JOIN itineraries ON itineraries.id = days.itinerary_id 
    WHERE days.id = itinerary_items.day_id 
    AND itineraries.user_id = auth.uid()
  )
);
CREATE POLICY "Anyone can view items of public shared itineraries" ON itinerary_items FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM days 
    JOIN itineraries ON itineraries.id = days.itinerary_id 
    WHERE days.id = itinerary_items.day_id 
    AND itineraries.is_public = true
  )
);
CREATE POLICY "Users can insert own itinerary items" ON itinerary_items FOR INSERT WITH CHECK (
  EXISTS (
    SELECT 1 FROM days 
    JOIN itineraries ON itineraries.id = days.itinerary_id 
    WHERE days.id = itinerary_items.day_id 
    AND itineraries.user_id = auth.uid()
  )
);
CREATE POLICY "Users can update own itinerary items" ON itinerary_items FOR UPDATE USING (
  EXISTS (
    SELECT 1 FROM days 
    JOIN itineraries ON itineraries.id = days.itinerary_id 
    WHERE days.id = itinerary_items.day_id 
    AND itineraries.user_id = auth.uid()
  )
);
CREATE POLICY "Users can delete own itinerary items" ON itinerary_items FOR DELETE USING (
  EXISTS (
    SELECT 1 FROM days 
    JOIN itineraries ON itineraries.id = days.itinerary_id 
    WHERE days.id = itinerary_items.day_id 
    AND itineraries.user_id = auth.uid()
  )
);

-- Create function to handle user profile creation
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.profiles (id, email, full_name)
  VALUES (NEW.id, NEW.email, NEW.raw_user_meta_data->>'full_name');
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to automatically create profile on user signup
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for updated_at
CREATE TRIGGER update_profiles_updated_at BEFORE UPDATE ON profiles
  FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_itineraries_updated_at BEFORE UPDATE ON itineraries
  FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_days_updated_at BEFORE UPDATE ON days
  FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_itinerary_items_updated_at BEFORE UPDATE ON itinerary_items
  FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();
